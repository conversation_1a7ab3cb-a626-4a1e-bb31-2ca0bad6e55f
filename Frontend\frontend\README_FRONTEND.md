# React Frontend

This is the React frontend for the full-stack React + Laravel application, built with Vite and modern 2025 practices.

## 🚀 Features

- React 18 with modern hooks
- Vite 7 for lightning-fast development
- Axios for API communication
- TypeScript support ready
- Hot Module Replacement (HMR)
- API proxy configuration
- Modern ES6+ JavaScript

## 📁 Project Structure

```
src/
├── services/
│   └── api.js          # API service layer
├── assets/
│   └── react.svg
├── App.jsx             # Main application component
├── App.css             # Application styles
├── index.css           # Global styles
└── main.jsx            # Application entry point
```

## 🔧 Configuration

### Vite Configuration (`vite.config.js`)
```javascript
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      "/api": {
        target: "http://127.0.0.1:8000",
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
```

### API Service (`src/services/api.js`)
- Axios instance with base configuration
- Request/response interceptors
- Authentication token handling
- Error handling
- Predefined API methods

## 📡 API Integration

### Available API Methods
```javascript
import { apiService } from './services/api';

// Health check
await apiService.healthCheck();

// Posts
await apiService.getPosts();
await apiService.getPost(id);
await apiService.createPost(data);

// Authentication (for future use)
await apiService.login(credentials);
await apiService.register(userData);
await apiService.logout();
await apiService.getUser();
```

### Authentication
The API service automatically:
- Adds Bearer token to requests if available
- Stores tokens in localStorage
- Handles 401 unauthorized responses
- Redirects on authentication failures

## 🛠️ Development Commands

### Start Development Server
```bash
npm run dev
```

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Lint Code
```bash
npm run lint
```

## 🎨 Components

### Main App Component
The `App.jsx` component demonstrates:
- API health checking
- Fetching and displaying data from Laravel API
- Error handling
- Loading states
- Real-time API status updates

### Features Demonstrated
- ✅ API connectivity testing
- ✅ Data fetching from Laravel backend
- ✅ Error handling and user feedback
- ✅ Loading states
- ✅ Interactive buttons for API calls
- ✅ Real-time status updates

## 🔄 State Management

Currently using React's built-in state management:
- `useState` for component state
- `useEffect` for side effects
- Local state for API responses

### Future Enhancements
Consider adding:
- Redux Toolkit for global state
- React Query for server state
- Context API for shared state
- Zustand for lightweight state management

## 🎯 API Communication Flow

1. **Component Mount**: Automatically checks API health and fetches posts
2. **API Calls**: Uses axios with interceptors for consistent handling
3. **Error Handling**: Displays user-friendly error messages
4. **Loading States**: Shows loading indicators during API calls
5. **Data Display**: Renders API responses in the UI

## 📱 Responsive Design

The application uses:
- CSS Grid and Flexbox
- Responsive units
- Mobile-first approach (ready for implementation)

## 🔐 Security Considerations

- API tokens stored in localStorage
- CORS handled by backend
- Input sanitization (to be implemented)
- XSS protection (to be implemented)

## 📝 Next Steps

1. Add more React components
2. Implement routing with React Router
3. Add form handling and validation
4. Implement user authentication UI
5. Add state management (Redux/Zustand)
6. Create reusable component library
7. Add unit and integration tests
8. Implement responsive design
9. Add error boundaries
10. Optimize performance with React.memo and useMemo

## 🧪 Testing

Future testing setup:
- Jest for unit tests
- React Testing Library for component tests
- Cypress for E2E tests
- MSW for API mocking

## 🚀 Deployment

For production deployment:
1. Run `npm run build`
2. Deploy `dist/` folder to web server
3. Configure environment variables
4. Set up proper routing for SPA
5. Configure HTTPS and security headers
