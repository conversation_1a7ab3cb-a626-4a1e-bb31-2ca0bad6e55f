import { useState, useEffect } from "react";
import reactLogo from "./assets/react.svg";
import viteLogo from "/vite.svg";
import "./App.css";
import { apiService } from "./services/api";

function App() {
  const [count, setCount] = useState(0);
  const [apiStatus, setApiStatus] = useState("checking...");
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(false);

  // Check API health on component mount
  useEffect(() => {
    checkApiHealth();
    fetchPosts();
  }, []);

  const checkApiHealth = async () => {
    try {
      console.log("🔍 Checking API health...");
      const response = await apiService.healthCheck();
      console.log("✅ API Response:", response.data);
      setApiStatus(`✅ Connected - ${response.data.message}`);
    } catch (error) {
      console.error("❌ API Health Check Failed:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        url: error.config?.url,
      });
      setApiStatus(`❌ API Connection Failed - ${error.message}`);
    }
  };

  const fetchPosts = async () => {
    setLoading(true);
    try {
      console.log("📄 Fetching posts...");
      const response = await apiService.getPosts();
      console.log("✅ Posts Response:", response.data);
      setPosts(response.data.data);
    } catch (error) {
      console.error("❌ Failed to fetch posts:", error);
      console.error("Posts error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        url: error.config?.url,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>React + Laravel Full Stack</h1>

      <div className="card">
        <h3>API Status</h3>
        <p>{apiStatus}</p>
        <div style={{ display: "flex", gap: "10px", justifyContent: "center" }}>
          <button onClick={checkApiHealth}>Refresh API Status</button>
          <button
            onClick={() => window.location.reload()}
            style={{ background: "#ff6b6b" }}
          >
            🔄 Hard Reload
          </button>
        </div>
        <small style={{ display: "block", marginTop: "10px", color: "#666" }}>
          إذا كان الـ API لا يعمل، اضغط F12 وتحقق من Console للتفاصيل
        </small>
      </div>

      <div className="card">
        <h3>Sample Posts from Laravel API</h3>
        {loading ? (
          <p>Loading posts...</p>
        ) : (
          <div>
            {posts.map((post) => (
              <div
                key={post.id}
                style={{
                  border: "1px solid #ccc",
                  margin: "10px",
                  padding: "10px",
                }}
              >
                <h4>{post.title}</h4>
                <p>{post.content}</p>
              </div>
            ))}
          </div>
        )}
        <button onClick={fetchPosts}>Refresh Posts</button>
      </div>

      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.jsx</code> and save to test HMR
        </p>
      </div>

      <p className="read-the-docs">
        Full-stack React + Laravel application with API communication
      </p>
    </>
  );
}

export default App;
