<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public API routes
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'message' => 'API is working',
        'timestamp' => now()
    ]);
});

// Sample API endpoints
Route::prefix('v1')->group(function () {
    // Public routes
    Route::get('/posts', function () {
        return response()->json([
            'data' => [
                ['id' => 1, 'title' => 'First Post', 'content' => 'This is the first post'],
                ['id' => 2, 'title' => 'Second Post', 'content' => 'This is the second post'],
            ]
        ]);
    });

    Route::get('/posts/{id}', function ($id) {
        return response()->json([
            'data' => ['id' => $id, 'title' => 'Post ' . $id, 'content' => 'Content for post ' . $id]
        ]);
    });

    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/posts', function (Request $request) {
            return response()->json([
                'message' => 'Post created successfully',
                'data' => [
                    'id' => rand(1000, 9999),
                    'title' => $request->input('title'),
                    'content' => $request->input('content'),
                    'created_at' => now()
                ]
            ], 201);
        });
    });
});
