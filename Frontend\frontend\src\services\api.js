import axios from "axios";

// Create axios instance with base configuration
const api = axios.create({
  baseURL: "/api", // استخدام الـ proxy بدلاً من الرابط المباشر
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem("auth_token");
      // Redirect to login or handle as needed
    }
    return Promise.reject(error);
  }
);

// API methods
export const apiService = {
  // Health check
  healthCheck: () => api.get("/health"),

  // Posts endpoints
  getPosts: () => api.get("/v1/posts"),
  getPost: (id) => api.get(`/v1/posts/${id}`),
  createPost: (data) => api.post("/v1/posts", data),

  // Auth endpoints (for future use)
  login: (credentials) => api.post("/auth/login", credentials),
  register: (userData) => api.post("/auth/register", userData),
  logout: () => api.post("/auth/logout"),
  getUser: () => api.get("/user"),
};

export default api;
