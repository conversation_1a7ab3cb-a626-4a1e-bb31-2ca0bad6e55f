# Full-Stack React + <PERSON>vel Application (2025)

A modern full-stack web application built with React.js frontend and Laravel backend API, featuring the latest 2025 versions and best practices.

## 🚀 Tech Stack

### Frontend
- **React 18** - Modern React with hooks
- **Vite 7** - Lightning-fast build tool and dev server
- **Axios** - HTTP client for API communication
- **Modern JavaScript/ES6+** - Latest JavaScript features

### Backend
- **Laravel 12** - Latest Laravel framework (2025)
- **<PERSON>vel Sanctum** - API authentication
- **PHP 8.2+** - Modern PHP features
- **Composer** - Dependency management

## 📁 Project Structure

```
Frontend/
├── backend/          # Laravel API backend
│   ├── app/
│   ├── routes/
│   │   ├── api.php   # API routes
│   │   └── web.php
│   ├── config/
│   └── ...
├── frontend/         # React frontend
│   ├── src/
│   │   ├── services/
│   │   │   └── api.js    # API service layer
│   │   ├── App.jsx       # Main React component
│   │   └── ...
│   ├── package.json
│   └── vite.config.js
└── README.md
```

## 🛠️ Installation & Setup

### Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js 22+ 
- npm

### Backend Setup (Laravel)

1. Navigate to backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
composer install
```

3. Set up environment:
```bash
copy .env.example .env
php artisan key:generate
```

4. Publish Sanctum configuration:
```bash
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

5. Start the Laravel development server:
```bash
php artisan serve --host=127.0.0.1 --port=8000
```

### Frontend Setup (React)

1. Navigate to frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the React development server:
```bash
npm run dev
```

## 🌐 Running the Application

1. **Start Laravel API** (Terminal 1):
```bash
cd backend
php artisan serve --host=127.0.0.1 --port=8000
```

2. **Start React Frontend** (Terminal 2):
```bash
cd frontend
npm run dev
```

3. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://127.0.0.1:8000/api

## 📡 API Endpoints

### Public Endpoints
- `GET /api/health` - API health check
- `GET /api/v1/posts` - Get all posts
- `GET /api/v1/posts/{id}` - Get specific post

### Protected Endpoints (require authentication)
- `POST /api/v1/posts` - Create new post
- `GET /api/user` - Get authenticated user

## 🔧 Features

- ✅ Modern React with Vite
- ✅ Laravel 12 API backend
- ✅ CORS configuration
- ✅ API authentication with Sanctum
- ✅ Axios HTTP client
- ✅ Development proxy setup
- ✅ Sample API endpoints
- ✅ Real-time API status checking
- ✅ Error handling

## 🚀 Development

The application includes:
- Hot module replacement (HMR) for React
- API proxy configuration in Vite
- CORS headers for cross-origin requests
- Structured API service layer
- Sample data and endpoints for testing

## 📝 Next Steps

1. Set up database and migrations
2. Implement user authentication
3. Add more API endpoints
4. Create additional React components
5. Add state management (Redux/Zustand)
6. Implement testing (Jest/PHPUnit)
7. Set up production deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).
