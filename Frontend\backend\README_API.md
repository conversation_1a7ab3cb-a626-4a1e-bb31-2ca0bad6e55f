# Laravel API Backend

This is the Laravel 12 backend API for the full-stack React + Laravel application.

## 🚀 Features

- Laravel 12 (latest 2025 version)
- Laravel Sanctum for API authentication
- CORS configuration for frontend communication
- RESTful API endpoints
- Sample data endpoints

## 📡 API Endpoints

### Health Check
```
GET /api/health
```
Response:
```json
{
  "status": "ok",
  "message": "API is working",
  "timestamp": "2025-07-06T15:54:41.888524Z"
}
```

### Posts API (v1)

#### Get All Posts
```
GET /api/v1/posts
```
Response:
```json
{
  "data": [
    {
      "id": 1,
      "title": "First Post",
      "content": "This is the first post"
    },
    {
      "id": 2,
      "title": "Second Post", 
      "content": "This is the second post"
    }
  ]
}
```

#### Get Single Post
```
GET /api/v1/posts/{id}
```
Response:
```json
{
  "data": {
    "id": 1,
    "title": "Post 1",
    "content": "Content for post 1"
  }
}
```

#### Create Post (Protected)
```
POST /api/v1/posts
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "New Post Title",
  "content": "Post content here"
}
```
Response:
```json
{
  "message": "Post created successfully",
  "data": {
    "id": 1234,
    "title": "New Post Title",
    "content": "Post content here",
    "created_at": "2025-07-06T16:00:00.000000Z"
  }
}
```

### Authentication

#### Get User (Protected)
```
GET /api/user
Authorization: Bearer {token}
```

## 🔧 Configuration

### CORS Settings
Located in `config/cors.php`:
- Allows all origins (`*`)
- Allows all methods
- Allows all headers
- Configured for API routes (`api/*`)

### Sanctum Configuration
- Published configuration in `config/sanctum.php`
- Middleware configured in `bootstrap/app.php`
- Ready for token-based authentication

## 🛠️ Development Commands

### Start Development Server
```bash
php artisan serve --host=127.0.0.1 --port=8000
```

### Generate Application Key
```bash
php artisan key:generate
```

### Clear Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

### Run Tests
```bash
php artisan test
```

## 📁 Key Files

- `routes/api.php` - API route definitions
- `bootstrap/app.php` - Application bootstrap with middleware
- `config/cors.php` - CORS configuration
- `config/sanctum.php` - Sanctum authentication config
- `.env` - Environment configuration

## 🔐 Security

- CORS properly configured
- Sanctum for API authentication
- Environment variables for sensitive data
- Input validation (to be implemented)
- Rate limiting (to be implemented)

## 📝 Next Steps

1. Set up database and migrations
2. Create proper models and controllers
3. Implement user registration/login
4. Add input validation
5. Implement rate limiting
6. Add comprehensive error handling
7. Set up logging and monitoring
